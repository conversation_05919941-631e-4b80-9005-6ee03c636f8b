package com.example.fayao.ui

import android.widget.Toast
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.example.fayao.data.ExportManager
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun ExportScreen() {
    val context = LocalContext.current
    val exportManager = remember { ExportManager(context) }
    var exportStatus by remember { mutableStateOf("") }
    
    var startDate by remember { mutableStateOf(Date()) }
    var endDate by remember { mutableStateOf(Date()) }
    var showStartDatePicker by remember { mutableStateOf(false) }
    var showEndDatePicker by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "数据导出",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "按时间范围导出",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Button(
                        onClick = { showStartDatePicker = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.DateRange, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("开始日期")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(startDate),
                        modifier = Modifier
                            .weight(1f)
                            .align(Alignment.CenterVertically)
                    )
                }
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Button(
                        onClick = { showEndDatePicker = true },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(Icons.Default.DateRange, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("结束日期")
                    }
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(endDate),
                        modifier = Modifier
                            .weight(1f)
                            .align(Alignment.CenterVertically)
                    )
                }
                
                Button(
                    onClick = {
                        val result = exportManager.exportDataByTimeRange(startDate, endDate)
                        if (result != null) {
                            exportStatus = "数据导出成功: $result"
                            Toast.makeText(context, "数据导出成功", Toast.LENGTH_SHORT).show()
                        } else {
                            exportStatus = "数据导出失败"
                            Toast.makeText(context, "数据导出失败", Toast.LENGTH_SHORT).show()
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !startDate.after(endDate)
                ) {
                    Text("导出选定时间范围数据")
                }
            }
        }

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "导出所有未导出数据",
                    style = MaterialTheme.typography.titleMedium,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Button(
                    onClick = {
                        val result = exportManager.exportAllUndeliveredData()
                        if (result != null) {
                            exportStatus = "所有数据导出成功: $result"
                            Toast.makeText(context, "所有数据导出成功", Toast.LENGTH_SHORT).show()
                        } else {
                            exportStatus = "数据导出失败"
                            Toast.makeText(context, "数据导出失败", Toast.LENGTH_SHORT).show()
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("导出所有未导出数据")
                }
            }
        }

        if (exportStatus.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "导出状态:",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    Text(text = exportStatus)
                }
            }
        }
    }

    if (showStartDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showStartDatePicker = false },
            confirmButton = {
                // 在实际实现中，这里需要处理日期选择
                showStartDatePicker = false
            }
        ) {
            DatePicker(state = rememberDatePickerState())
        }
    }

    if (showEndDatePicker) {
        DatePickerDialog(
            onDismissRequest = { showEndDatePicker = false },
            confirmButton = {
                // 在实际实现中，这里需要处理日期选择
                showEndDatePicker = false
            }
        ) {
            DatePicker(state = rememberDatePickerState())
        }
    }
}