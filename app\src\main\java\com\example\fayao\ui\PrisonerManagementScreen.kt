package com.example.fayao.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun PrisonerManagementScreen() {
    var showAddPrisonerDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "罪犯列表",
                style = MaterialTheme.typography.headlineSmall
            )

            FloatingActionButton(
                onClick = { showAddPrisonerDialog = true }
            ) {
                Icon(Icons.Filled.Add, contentDescription = "添加罪犯")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 显示罪犯列表
        Text(
            text = "这里将显示罪犯列表",
            modifier = Modifier.fillMaxWidth(),
            style = MaterialTheme.typography.bodyLarge
        )
    }

    if (showAddPrisonerDialog) {
        AddPrisonerDialog(
            onDismiss = { showAddPrisonerDialog = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddPrisonerDialog(onDismiss: () -> Unit) {
    var ward by remember { mutableStateOf("") }
    var number by remember { mutableStateOf("") }
    var name by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加罪犯") },
        text = {
            Column {
                OutlinedTextField(
                    value = ward,
                    onValueChange = { ward = it },
                    label = { Text("监区") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                )

                OutlinedTextField(
                    value = number,
                    onValueChange = { number = it },
                    label = { Text("编号") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                )

                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("姓名") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                )

                // 处方照片上传按钮
                Button(
                    onClick = { /* 处理处方照片上传 */ },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                ) {
                    Text("上传处方照片")
                }

                // 指纹录入按钮
                Button(
                    onClick = { /* 处理指纹录入 */ },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("录入指纹信息")
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    // 保存罪犯信息
                    onDismiss()
                }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}