package com.example.fayao.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.BarChart
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.example.fayao.data.PrisonerDao

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(prisonerDao: PrisonerDao) {
    var selectedItem by remember { mutableStateOf(0) }
    val items = listOf("罪犯管理", "发药记录", "查询", "统计", "导出")

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (selectedItem) {
                            0 -> "罪犯管理"
                            1 -> "发药记录"
                            2 -> "查询"
                            3 -> "统计"
                            4 -> "数据导出"
                            else -> "药品管理系统"
                        }
                    )
                }
            )
        },
        bottomBar = {
            NavigationBar {
                NavigationBarItem(
                    icon = { Icon(Icons.Filled.Add, contentDescription = null) },
                    label = { Text(items[0]) },
                    selected = selectedItem == 0,
                    onClick = { selectedItem = 0 }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Filled.List, contentDescription = null) },
                    label = { Text(items[1]) },
                    selected = selectedItem == 1,
                    onClick = { selectedItem = 1 }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Filled.Search, contentDescription = null) },
                    label = { Text(items[2]) },
                    selected = selectedItem == 2,
                    onClick = { selectedItem = 2 }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Filled.BarChart, contentDescription = null) },
                    label = { Text(items[3]) },
                    selected = selectedItem == 3,
                    onClick = { selectedItem = 3 }
                )
                NavigationBarItem(
                    icon = { Icon(Icons.Filled.CloudUpload, contentDescription = null) },
                    label = { Text(items[4]) },
                    selected = selectedItem == 4,
                    onClick = { selectedItem = 4 }
                )
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            when (selectedItem) {
                0 -> PrisonerManagementScreen(prisonerDao)
                1 -> MedicationRecordsScreen(prisonerDao)
                2 -> SearchScreen(prisonerDao)
                3 -> StatisticsScreen()
                4 -> ExportScreen()
                else -> PrisonerManagementScreen(prisonerDao)
            }
        }
    }
}