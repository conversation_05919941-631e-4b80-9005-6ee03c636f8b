package com.example.fayao.fingerprint

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.hardware.fingerprint.FingerprintManager
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.hardware.fingerprint.FingerprintManagerCompat
import androidx.core.os.CancellationSignal
import com.huawei.hms.hmsscankit.ScanKitFactory
import com.huawei.hms.ml.scan.HmsScanAnalyzerOptions
import com.huawei.hms.support.hwid.HuaweiIdAuthManager

class FingerprintManager(private val context: Context) {
    
    private val fingerprintManager = FingerprintManagerCompat.from(context)
    private val TAG = "FingerprintManager"
    
    /**
     * 检查指纹硬件是否可用
     * @return 是否可用
     */
    fun isFingerprintHardwareAvailable(): Boolean {
        return fingerprintManager.isHardwareDetected
    }
    
    /**
     * 检查是否有录入的指纹
     * @return 是否有录入指纹
     */
    fun hasEnrolledFingerprints(): Boolean {
        return fingerprintManager.hasEnrolledFingerprints()
    }
    
    /**
     * 开始指纹识别
     * @param callback 指纹识别回调
     */
    fun startFingerprintAuthentication(callback: FingerprintCallback) {
        if (!isFingerprintHardwareAvailable()) {
            callback.onFailure("设备不支持指纹识别")
            return
        }
        
        if (!hasEnrolledFingerprints()) {
            callback.onFailure("未录入指纹")
            return
        }
        
        val cancellationSignal = CancellationSignal()
        val cryptoObject = null // 在实际应用中应使用加密对象
        
        fingerprintManager.authenticate(
            cryptoObject,
            0,
            cancellationSignal,
            object : FingerprintManagerCompat.AuthenticationCallback() {
                override fun onAuthenticationError(errMsgId: Int, errString: CharSequence?) {
                    Log.e(TAG, "指纹识别错误: $errString")
                    callback.onFailure("指纹识别错误: $errString")
                }
                
                override fun onAuthenticationHelp(helpMsgId: Int, helpString: CharSequence?) {
                    Log.w(TAG, "指纹识别帮助: $helpString")
                    callback.onFailure("指纹识别帮助: $helpString")
                }
                
                override fun onAuthenticationSucceeded(result: FingerprintManagerCompat.AuthenticationResult?) {
                    Log.d(TAG, "指纹识别成功")
                    callback.onSuccess()
                }
                
                override fun onAuthenticationFailed() {
                    Log.w(TAG, "指纹识别失败，请重试")
                    callback.onFailure("指纹识别失败，请重试")
                }
            },
            null
        )
    }
    
    /**
     * 开始华为指纹识别（如果可用）
     * @param callback 指纹识别回调
     */
    fun startHuaweiFingerprintAuthentication(callback: FingerprintCallback) {
        try {
            // 初始化华为HMS服务
            // 在实际应用中，您需要按照华为开发者文档正确初始化HMS服务
            // 这里仅提供示例代码框架
            
            // 检查华为移动服务是否可用
            // val hwidAuthService = HuaweiIdAuthManager.getService(context, HuaweiIdAuthParams.DEFAULT_AUTH_REQUEST_PARAM)
            
            // 使用华为指纹SDK进行认证
            // 这里需要根据华为指纹SDK的具体API进行实现
            startFingerprintAuthentication(callback)
        } catch (e: Exception) {
            Log.e(TAG, "华为指纹识别初始化失败", e)
            // 如果华为指纹识别不可用，回退到系统指纹识别
            startFingerprintAuthentication(callback)
        }
    }
    
    /**
     * 指纹识别回调接口
     */
    interface FingerprintCallback {
        fun onSuccess()
        fun onFailure(message: String)
    }
}