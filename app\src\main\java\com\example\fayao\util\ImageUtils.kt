package com.example.fayao.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ImageUtils {
    
    companion object {
        /**
         * 保存Bitmap到文件
         * @param context 上下文
         * @param bitmap 要保存的Bitmap
         * @param prefix 文件名前缀
         * @return 保存的文件路径
         */
        fun saveBitmapToFile(context: Context, bitmap: Bitmap, prefix: String): String? {
            return try {
                val imagesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES) ?: context.filesDir
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val imageFile = File(imagesDir, "${prefix}_${timeStamp}.jpg")
                
                val out = FileOutputStream(imageFile)
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
                out.flush()
                out.close()
                
                imageFile.absolutePath
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }
        
        /**
         * 从Uri加载Bitmap
         * @param context 上下文
         * @param uri 图片Uri
         * @param maxWidth 最大宽度
         * @param maxHeight 最大高度
         * @return 加载的Bitmap
         */
        fun loadBitmapFromUri(context: Context, uri: Uri, maxWidth: Int = 1024, maxHeight: Int = 1024): Bitmap? {
            return try {
                val inputStream = context.contentResolver.openInputStream(uri)
                val options = BitmapFactory.Options()
                options.inJustDecodeBounds = true
                BitmapFactory.decodeStream(inputStream, null, options)
                inputStream?.close()
                
                // 计算缩放比例
                val scale = calculateInSampleSize(options, maxWidth, maxHeight)
                options.inJustDecodeBounds = false
                options.inSampleSize = scale
                
                inputStream?.let {
                    val newInputStream = context.contentResolver.openInputStream(uri)
                    val bitmap = BitmapFactory.decodeStream(newInputStream, null, options)
                    newInputStream?.close()
                    bitmap
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
        
        /**
         * 计算图片缩放比例
         * @param options BitmapFactory.Options
         * @param reqWidth 请求宽度
         * @param reqHeight 请求高度
         * @return 缩放比例
         */
        private fun calculateInSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
            val height = options.outHeight
            val width = options.outWidth
            var inSampleSize = 1
            
            if (height > reqHeight || width > reqWidth) {
                val halfHeight = height / 2
                val halfWidth = width / 2
                
                while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                    inSampleSize *= 2
                }
            }
            
            return inSampleSize
        }
        
        /**
         * 获取图片文件列表
         * @param context 上下文
         * @return 图片文件列表
         */
        fun getImageFiles(context: Context): List<File> {
            val imagesDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES) ?: context.filesDir
            val imageFiles = mutableListOf<File>()
            
            imagesDir.listFiles()?.forEach { file ->
                if (file.isFile && (file.name.endsWith(".jpg") || file.name.endsWith(".png"))) {
                    imageFiles.add(file)
                }
            }
            
            return imageFiles.sortedByDescending { it.lastModified() }
        }
    }
}