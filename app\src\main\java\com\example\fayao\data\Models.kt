package com.example.fayao.data

import android.graphics.Bitmap
import java.util.Date

// 罪犯信息数据模型
data class Prisoner(
    val id: Long = 0,
    val ward: String,           // 监区
    val number: String,         // 编号
    val name: String,           // 姓名
    val prescriptionPhotoPath: String?, // 处方照片路径
    val fingerprintTemplate: ByteArray?, // 指纹模板数据
    val addedTime: Date         // 添加时间
) {
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Prisoner

        if (id != other.id) return false
        if (ward != other.ward) return false
        if (number != other.number) return false
        if (name != other.name) return false
        if (prescriptionPhotoPath != other.prescriptionPhotoPath) return false
        if (fingerprintTemplate != null) {
            if (other.fingerprintTemplate == null) return false
            if (!fingerprintTemplate.contentEquals(other.fingerprintTemplate)) return false
        } else if (other.fingerprintTemplate != null) return false
        if (addedTime != other.addedTime) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + ward.hashCode()
        result = 31 * result + number.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + (prescriptionPhotoPath?.hashCode() ?: 0)
        result = 31 * result + (fingerprintTemplate?.contentHashCode() ?: 0)
        result = 31 * result + addedTime.hashCode()
        return result
    }
}

// 处方信息数据模型
data class Prescription(
    val id: Long = 0,
    val prisonerId: Long,       // 关联的罪犯ID
    val photoPath: String,      // 处方照片路径
    val addedTime: Date         // 添加时间
)

// 服药记录数据模型
data class MedicationRecord(
    val id: Long = 0,
    val prisonerId: Long,       // 关联的罪犯ID
    val medicationTime: Date,   // 发药时间
    val fingerprintPath: String?, // 指纹识别记录路径
    val facePhotoPath: String?, // 面部照片路径
    val prescriptionPhotoPath: String? // 当时的处方照片路径
)

// 统计数据模型
data class Statistics(
    val period: String,         // 统计周期
    val totalPrisoners: Int,    // 总罪犯数
    val totalMedications: Int,  // 总服药次数
    val medicationsByWard: Map<String, Int> // 各监区服药次数
)