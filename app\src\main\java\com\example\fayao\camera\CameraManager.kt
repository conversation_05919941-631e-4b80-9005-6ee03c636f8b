package com.example.fayao.camera

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import java.io.File

class CameraManager {
    companion object {
        /**
         * 创建拍照意图
         * @param context 上下文
         * @return 拍照意图和照片文件
         */
        fun createCaptureIntent(context: Context): Pair<Intent, File> {
            val photoFile = File.createTempFile(
                "photo_${System.currentTimeMillis()}_",
                ".jpg",
                context.cacheDir
            )
            
            val photoUri = Uri.fromFile(photoFile)
            val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE).apply {
                putExtra(MediaStore.EXTRA_OUTPUT, photoUri)
            }
            
            return Pair(intent, photoFile)
        }
        
        /**
         * 创建选择照片意图
         * @return 选择照片意图
         */
        fun createPickIntent(): Intent {
            return Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        }
    }
}

@Composable
fun rememberCameraLauncher(onResult: (Uri?) -> Unit): ManagedActivityResultLauncher<Intent, Boolean> {
    val context = LocalContext.current
    return rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            val uri = result.data?.data
            onResult(uri)
        } else {
            onResult(null)
        }
    }
}

@Composable
fun rememberImagePickerLauncher(onResult: (Uri?) -> Unit): ManagedActivityResultLauncher<Intent, Boolean> {
    return rememberLauncherForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            val uri = result.data?.data
            onResult(uri)
        } else {
            onResult(null)
        }
    }
}