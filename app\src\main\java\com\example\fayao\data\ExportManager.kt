package com.example.fayao.data

import android.content.Context
import android.net.Uri
import android.os.Environment
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ExportManager(private val context: Context) {
    
    /**
     * 导出指定时间段内的数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param exportDir 导出目录
     * @return 导出的文件路径
     */
    fun exportDataByTimeRange(
        startTime: Date,
        endTime: Date,
        exportDir: File = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS) ?: context.filesDir
    ): String? {
        try {
            // 创建导出目录
            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            val timestamp = dateFormat.format(Date())
            val exportFolder = File(exportDir, "fayao_export_$timestamp")
            if (!exportFolder.exists()) {
                exportFolder.mkdirs()
            }
            
            // 导出数据库
            exportDatabase(exportFolder)
            
            // 导出图片
            exportImages(exportFolder, startTime, endTime)
            
            return exportFolder.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * 导出未导出的所有数据
     * @param exportDir 导出目录
     * @return 导出的文件路径
     */
    fun exportAllUndeliveredData(
        exportDir: File = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS) ?: context.filesDir
    ): String? {
        try {
            // 创建导出目录
            val dateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
            val timestamp = dateFormat.format(Date())
            val exportFolder = File(exportDir, "fayao_export_all_$timestamp")
            if (!exportFolder.exists()) {
                exportFolder.mkdirs()
            }
            
            // 导出数据库
            exportDatabase(exportFolder)
            
            // 导出所有图片
            exportAllImages(exportFolder)
            
            return exportFolder.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * 导出数据库文件
     * @param exportFolder 导出目录
     */
    private fun exportDatabase(exportFolder: File) {
        try {
            val dbFile = context.getDatabasePath("Fayao.db")
            val exportFile = File(exportFolder, "Fayao.db")
            
            dbFile.copyTo(exportFile, overwrite = true)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 导出指定时间范围内的图片
     * @param exportFolder 导出目录
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private fun exportImages(exportFolder: File, startTime: Date, endTime: Date) {
        // 实现图片导出逻辑
        // 这里应该查询数据库获取指定时间范围内的记录，然后复制相关图片文件
    }
    
    /**
     * 导出所有图片
     * @param exportFolder 导出目录
     */
    private fun exportAllImages(exportFolder: File) {
        // 实现所有图片导出逻辑
        // 复制所有处方照片、指纹照片和面部照片到导出目录
    }
    
    /**
     * 获取导出文件列表
     * @return 导出文件列表
     */
    fun getExportFiles(): List<File> {
        val exportDir = context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS) ?: context.filesDir
        val exportFiles = mutableListOf<File>()
        
        exportDir.listFiles()?.forEach { file ->
            if (file.isDirectory && file.name.startsWith("fayao_export")) {
                exportFiles.add(file)
            }
        }
        
        return exportFiles.sortedByDescending { it.lastModified() }
    }
}