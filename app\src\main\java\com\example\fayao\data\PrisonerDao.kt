package com.example.fayao.data

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.sqlite.SQLiteDatabase
import java.util.Date

class PrisonerDao(private val dbHelper: DatabaseHelper) {
    private var database: SQLiteDatabase? = null

    fun open(): PrisonerDao {
        database = dbHelper.writableDatabase
        // 检查是否需要初始化模拟数据
        initializeMockDataIfNeeded()
        return this
    }

    fun close() {
        dbHelper.close()
    }
    
    /**
     * 初始化模拟数据（如果尚未初始化）
     */
    private fun initializeMockDataIfNeeded() {
        database?.let { db ->
            if (!dbHelper.isMockDataInitialized(db)) {
                // 生成模拟罪犯数据
                val mockPrisoners = MockDataGenerator.generateMockPrisoners(100)
                mockPrisoners.forEach { prisoner ->
                    addPrisoner(prisoner)
                }
                
                // 为前10个罪犯生成模拟服药记录
                mockPrisoners.take(10).forEach { prisoner ->
                    val records = MockDataGenerator.generateMockMedicationRecords(prisoner.id, (5..20).random())
                    records.forEach { record ->
                        addMedicationRecord(record)
                    }
                }
                
                // 标记模拟数据已初始化
                dbHelper.markMockDataAsInitialized(db)
            }
        }
    }

    // 添加罪犯
    fun addPrisoner(prisoner: Prisoner): Long {
        val values = ContentValues().apply {
            put(DatabaseHelper.COLUMN_PRISONER_WARD, prisoner.ward)
            put(DatabaseHelper.COLUMN_PRISONER_NUMBER, prisoner.number)
            put(DatabaseHelper.COLUMN_PRISONER_NAME, prisoner.name)
            put(DatabaseHelper.COLUMN_PRISONER_PRESCRIPTION_PHOTO, prisoner.prescriptionPhotoPath)
            put(DatabaseHelper.COLUMN_PRISONER_FINGERPRINT, prisoner.fingerprintTemplate)
            put(DatabaseHelper.COLUMN_PRISONER_ADDED_TIME, prisoner.addedTime.time)
        }

        return database?.insert(DatabaseHelper.TABLE_PRISONERS, null, values) ?: -1
    }

    // 更新罪犯指纹信息
    fun updatePrisonerFingerprint(id: Long, fingerprintTemplate: ByteArray): Int {
        val values = ContentValues().apply {
            put(DatabaseHelper.COLUMN_PRISONER_FINGERPRINT, fingerprintTemplate)
        }

        return database?.update(
            DatabaseHelper.TABLE_PRISONERS,
            values,
            "${DatabaseHelper.COLUMN_PRISONER_ID} = ?",
            arrayOf(id.toString())
        ) ?: 0
    }

    // 获取所有罪犯
    fun getAllPrisoners(): List<Prisoner> {
        val prisoners = mutableListOf<Prisoner>()
        val cursor = database?.query(
            DatabaseHelper.TABLE_PRISONERS,
            null,
            null,
            null,
            null,
            null,
            "${DatabaseHelper.COLUMN_PRISONER_ADDED_TIME} DESC"
        )

        cursor?.use {
            if (it.moveToFirst()) {
                do {
                    prisoners.add(cursorToPrisoner(it))
                } while (it.moveToNext())
            }
        }

        return prisoners
    }

    // 根据ID获取罪犯
    fun getPrisonerById(id: Long): Prisoner? {
        val cursor = database?.query(
            DatabaseHelper.TABLE_PRISONERS,
            null,
            "${DatabaseHelper.COLUMN_PRISONER_ID} = ?",
            arrayOf(id.toString()),
            null,
            null,
            null
        )

        cursor?.use {
            if (it.moveToFirst()) {
                return cursorToPrisoner(it)
            }
        }

        return null
    }

    // 根据编号获取罪犯
    fun getPrisonerByNumber(number: String): Prisoner? {
        val cursor = database?.query(
            DatabaseHelper.TABLE_PRISONERS,
            null,
            "${DatabaseHelper.COLUMN_PRISONER_NUMBER} = ?",
            arrayOf(number),
            null,
            null,
            null
        )

        cursor?.use {
            if (it.moveToFirst()) {
                return cursorToPrisoner(it)
            }
        }

        return null
    }

    // 根据监区获取罪犯
    fun getPrisonersByWard(ward: String): List<Prisoner> {
        val prisoners = mutableListOf<Prisoner>()
        val cursor = database?.query(
            DatabaseHelper.TABLE_PRISONERS,
            null,
            "${DatabaseHelper.COLUMN_PRISONER_WARD} = ?",
            arrayOf(ward),
            null,
            null,
            "${DatabaseHelper.COLUMN_PRISONER_ADDED_TIME} DESC"
        )

        cursor?.use {
            if (it.moveToFirst()) {
                do {
                    prisoners.add(cursorToPrisoner(it))
                } while (it.moveToNext())
            }
        }

        return prisoners
    }

    // 添加服药记录
    fun addMedicationRecord(record: MedicationRecord): Long {
        val values = ContentValues().apply {
            put(DatabaseHelper.COLUMN_RECORD_PRISONER_ID, record.prisonerId)
            put(DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME, record.medicationTime.time)
            put(DatabaseHelper.COLUMN_RECORD_FINGERPRINT_PHOTO, record.fingerprintPath)
            put(DatabaseHelper.COLUMN_RECORD_FACE_PHOTO, record.facePhotoPath)
            put(DatabaseHelper.COLUMN_RECORD_PRESCRIPTION_PHOTO, record.prescriptionPhotoPath)
        }

        return database?.insert(DatabaseHelper.TABLE_MEDICATION_RECORDS, null, values) ?: -1
    }

    // 获取指定罪犯的所有服药记录
    fun getMedicationRecordsByPrisonerId(prisonerId: Long): List<MedicationRecord> {
        val records = mutableListOf<MedicationRecord>()
        val cursor = database?.query(
            DatabaseHelper.TABLE_MEDICATION_RECORDS,
            null,
            "${DatabaseHelper.COLUMN_RECORD_PRISONER_ID} = ?",
            arrayOf(prisonerId.toString()),
            null,
            null,
            "${DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME} DESC"
        )

        cursor?.use {
            if (it.moveToFirst()) {
                do {
                    records.add(cursorToMedicationRecord(it))
                } while (it.moveToNext())
            }
        }

        return records
    }

    // 根据编号获取罪犯的服药记录
    fun getMedicationRecordsByPrisonerNumber(number: String): List<MedicationRecord> {
        val records = mutableListOf<MedicationRecord>()
        val query = """
            SELECT mr.* FROM ${DatabaseHelper.TABLE_MEDICATION_RECORDS} mr
            JOIN ${DatabaseHelper.TABLE_PRISONERS} p ON mr.${DatabaseHelper.COLUMN_RECORD_PRISONER_ID} = p.${DatabaseHelper.COLUMN_PRISONER_ID}
            WHERE p.${DatabaseHelper.COLUMN_PRISONER_NUMBER} = ?
            ORDER BY mr.${DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME} DESC
        """.trimIndent()

        val cursor = database?.rawQuery(query, arrayOf(number))

        cursor?.use {
            if (it.moveToFirst()) {
                do {
                    records.add(cursorToMedicationRecord(it))
                } while (it.moveToNext())
            }
        }

        return records
    }

    // 获取指定时间段内的服药记录
    fun getMedicationRecordsByTimeRange(startTime: Date, endTime: Date): List<MedicationRecord> {
        val records = mutableListOf<MedicationRecord>()
        val cursor = database?.query(
            DatabaseHelper.TABLE_MEDICATION_RECORDS,
            null,
            "${DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME} BETWEEN ? AND ?",
            arrayOf(startTime.time.toString(), endTime.time.toString()),
            null,
            null,
            "${DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME} DESC"
        )

        cursor?.use {
            if (it.moveToFirst()) {
                do {
                    records.add(cursorToMedicationRecord(it))
                } while (it.moveToNext())
            }
        }

        return records
    }

    // 获取所有未导出的服药记录
    fun getUndeliveredMedicationRecords(): List<MedicationRecord> {
        // 在实际实现中，我们可能需要一个字段来标记记录是否已导出
        // 这里暂时返回所有记录
        val records = mutableListOf<MedicationRecord>()
        val cursor = database?.query(
            DatabaseHelper.TABLE_MEDICATION_RECORDS,
            null,
            null,
            null,
            null,
            null,
            "${DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME} DESC"
        )

        cursor?.use {
            if (it.moveToFirst()) {
                do {
                    records.add(cursorToMedicationRecord(it))
                } while (it.moveToNext())
            }
        }

        return records
    }

    // 将Cursor转换为Prisoner对象
    private fun cursorToPrisoner(cursor: Cursor): Prisoner {
        return Prisoner(
            id = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_ID)),
            ward = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_WARD)),
            number = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_NUMBER)),
            name = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_NAME)),
            prescriptionPhotoPath = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_PRESCRIPTION_PHOTO)),
            fingerprintTemplate = cursor.getBlob(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_FINGERPRINT)),
            addedTime = Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_PRISONER_ADDED_TIME)))
        )
    }

    // 将Cursor转换为MedicationRecord对象
    private fun cursorToMedicationRecord(cursor: Cursor): MedicationRecord {
        return MedicationRecord(
            id = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_RECORD_ID)),
            prisonerId = cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_RECORD_PRISONER_ID)),
            medicationTime = Date(cursor.getLong(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_RECORD_MEDICATION_TIME))),
            fingerprintPath = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_RECORD_FINGERPRINT_PHOTO)),
            facePhotoPath = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_RECORD_FACE_PHOTO)),
            prescriptionPhotoPath = cursor.getString(cursor.getColumnIndexOrThrow(DatabaseHelper.COLUMN_RECORD_PRESCRIPTION_PHOTO))
        )
    }
}