package com.example.fayao.data

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper

class DatabaseHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    companion object {
        private const val DATABASE_NAME = "Fayao.db"
        private const val DATABASE_VERSION = 2 // 更新版本号

        // 罪犯表
        const val TABLE_PRISONERS = "prisoners"
        const val COLUMN_PRISONER_ID = "id"
        const val COLUMN_PRISONER_WARD = "ward"
        const val COLUMN_PRISONER_NUMBER = "number"
        const val COLUMN_PRISONER_NAME = "name"
        const val COLUMN_PRISONER_PRESCRIPTION_PHOTO = "prescription_photo"
        const val COLUMN_PRISONER_FINGERPRINT = "fingerprint"
        const val COLUMN_PRISONER_ADDED_TIME = "added_time"

        // 服药记录表
        const val TABLE_MEDICATION_RECORDS = "medication_records"
        const val COLUMN_RECORD_ID = "id"
        const val COLUMN_RECORD_PRISONER_ID = "prisoner_id"
        const val COLUMN_RECORD_MEDICATION_TIME = "medication_time"
        const val COLUMN_RECORD_FINGERPRINT_PHOTO = "fingerprint_photo"
        const val COLUMN_RECORD_FACE_PHOTO = "face_photo"
        const val COLUMN_RECORD_PRESCRIPTION_PHOTO = "prescription_photo"
        
        // 是否已初始化模拟数据的标记表
        const val TABLE_INIT_FLAG = "init_flag"
        const val COLUMN_INIT_FLAG_KEY = "key"
        const val COLUMN_INIT_FLAG_VALUE = "value"
    }

    override fun onCreate(db: SQLiteDatabase) {
        // 创建罪犯表
        val createPrisonersTable = """
            CREATE TABLE $TABLE_PRISONERS (
                $COLUMN_PRISONER_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                $COLUMN_PRISONER_WARD TEXT NOT NULL,
                $COLUMN_PRISONER_NUMBER TEXT NOT NULL UNIQUE,
                $COLUMN_PRISONER_NAME TEXT NOT NULL,
                $COLUMN_PRISONER_PRESCRIPTION_PHOTO TEXT,
                $COLUMN_PRISONER_FINGERPRINT BLOB,
                $COLUMN_PRISONER_ADDED_TIME INTEGER NOT NULL
            )
        """.trimIndent()

        // 创建服药记录表
        val createMedicationRecordsTable = """
            CREATE TABLE $TABLE_MEDICATION_RECORDS (
                $COLUMN_RECORD_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                $COLUMN_RECORD_PRISONER_ID INTEGER NOT NULL,
                $COLUMN_RECORD_MEDICATION_TIME INTEGER NOT NULL,
                $COLUMN_RECORD_FINGERPRINT_PHOTO TEXT,
                $COLUMN_RECORD_FACE_PHOTO TEXT,
                $COLUMN_RECORD_PRESCRIPTION_PHOTO TEXT,
                FOREIGN KEY ($COLUMN_RECORD_PRISONER_ID) REFERENCES $TABLE_PRISONERS ($COLUMN_PRISONER_ID)
            )
        """.trimIndent()
        
        // 创建初始化标记表
        val createInitFlagTable = """
            CREATE TABLE $TABLE_INIT_FLAG (
                $COLUMN_INIT_FLAG_KEY TEXT PRIMARY KEY,
                $COLUMN_INIT_FLAG_VALUE TEXT
            )
        """.trimIndent()

        db.execSQL(createPrisonersTable)
        db.execSQL(createMedicationRecordsTable)
        db.execSQL(createInitFlagTable)
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        if (oldVersion < 2) {
            // 创建初始化标记表
            val createInitFlagTable = """
                CREATE TABLE IF NOT EXISTS $TABLE_INIT_FLAG (
                    $COLUMN_INIT_FLAG_KEY TEXT PRIMARY KEY,
                    $COLUMN_INIT_FLAG_VALUE TEXT
                )
            """.trimIndent()
            db.execSQL(createInitFlagTable)
        } else {
            db.execSQL("DROP TABLE IF EXISTS $TABLE_MEDICATION_RECORDS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_PRISONERS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_INIT_FLAG")
            onCreate(db)
        }
    }
    
    /**
     * 检查是否已初始化模拟数据
     */
    fun isMockDataInitialized(db: SQLiteDatabase): Boolean {
        val cursor = db.query(
            TABLE_INIT_FLAG,
            arrayOf(COLUMN_INIT_FLAG_VALUE),
            "$COLUMN_INIT_FLAG_KEY = ?",
            arrayOf("mock_data_initialized"),
            null,
            null,
            null
        )
        
        cursor?.use {
            if (it.moveToFirst()) {
                val value = it.getString(it.getColumnIndexOrThrow(COLUMN_INIT_FLAG_VALUE))
                return value == "true"
            }
        }
        
        return false
    }
    
    /**
     * 标记模拟数据已初始化
     */
    fun markMockDataAsInitialized(db: SQLiteDatabase) {
        val values = ContentValues().apply {
            put(COLUMN_INIT_FLAG_KEY, "mock_data_initialized")
            put(COLUMN_INIT_FLAG_VALUE, "true")
        }
        
        db.insertWithOnConflict(
            TABLE_INIT_FLAG,
            null,
            values,
            SQLiteDatabase.CONFLICT_REPLACE
        )
    }
}