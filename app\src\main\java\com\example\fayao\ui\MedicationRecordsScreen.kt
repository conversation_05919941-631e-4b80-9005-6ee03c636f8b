package com.example.fayao.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun MedicationRecordsScreen() {
    var showAddMedicationDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "发药记录",
                style = MaterialTheme.typography.headlineSmall
            )

            FloatingActionButton(
                onClick = { showAddMedicationDialog = true }
            ) {
                Icon(Icons.Filled.Add, contentDescription = "添加发药记录")
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 显示发药记录列表
        Text(
            text = "这里将显示发药记录列表",
            modifier = Modifier.fillMaxWidth(),
            style = MaterialTheme.typography.bodyLarge
        )
    }

    if (showAddMedicationDialog) {
        AddMedicationDialog(
            onDismiss = { showAddMedicationDialog = false }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddMedicationDialog(onDismiss: () -> Unit) {
    var prisonerNumber by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("添加发药记录") },
        text = {
            Column {
                OutlinedTextField(
                    value = prisonerNumber,
                    onValueChange = { prisonerNumber = it },
                    label = { Text("罪犯编号") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                )

                // 显示处方照片
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                        .padding(bottom = 8.dp)
                ) {
                    // 处方照片显示区域
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text("处方照片将显示在这里")
                    }
                }

                // 指纹识别按钮
                Button(
                    onClick = { /* 处理指纹识别 */ },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp)
                ) {
                    Text("指纹识别")
                }

                // 自拍按钮
                Button(
                    onClick = { /* 处理自拍 */ },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text("拍照记录")
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    // 保存发药记录
                    onDismiss()
                }
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}