package com.example.fayao

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.*
import com.example.fayao.data.DatabaseHelper
import com.example.fayao.data.PrisonerDao
import com.example.fayao.ui.AppNavigation
import com.example.fayao.ui.theme.FayaoTheme

class MainActivity : ComponentActivity() {
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var prisonerDao: PrisonerDao
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        // 初始化数据库
        databaseHelper = DatabaseHelper(this)
        prisonerDao = PrisonerDao(databaseHelper)
        
        setContent {
            FayaoTheme {
                AppNavigation(prisonerDao)
            }
        }
    }
}

@Composable
fun AppNavigation(prisonerDao: PrisonerDao) {
    var isLoggedIn by remember { mutableStateOf(false) }

    if (isLoggedIn) {
        MainScreen(prisonerDao)
    } else {
        LoginScreen(
            onLoginSuccess = { isLoggedIn = true }
        )
    }
}

@Composable
fun Greeting(name: String, modifier: Modifier = Modifier) {
    Text(
        text = "Hello $name!",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    FayaoTheme {
        Greeting("Android")
    }
}