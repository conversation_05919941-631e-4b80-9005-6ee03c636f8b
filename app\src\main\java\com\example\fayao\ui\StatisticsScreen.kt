package com.example.fayao.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun StatisticsScreen() {
    var selectedPeriod by remember { mutableStateOf("日统计") }
    val periods = listOf("日统计", "周统计", "月统计")

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "服药统计",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            periods.forEach { period ->
                FilterChip(
                    selected = (period == selectedPeriod),
                    onClick = { selectedPeriod = period },
                    label = { Text(period) }
                )
            }
        }

        // 显示统计数据
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "统计信息将显示在这里",
                    style = MaterialTheme.typography.bodyLarge
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "总人数: 0",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Text(
                    text = "服药次数: 0",
                    style = MaterialTheme.typography.bodyMedium
                )
                
                Text(
                    text = "各监区统计:",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}