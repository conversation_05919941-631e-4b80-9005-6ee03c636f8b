package com.example.fayao.data

import android.content.Context
import java.util.*

class MockDataGenerator {
    
    companion object {
        /**
         * 生成模拟罪犯数据
         * @param count 要生成的罪犯数量
         * @return 罪犯列表
         */
        fun generateMockPrisoners(count: Int): List<Prisoner> {
            val prisoners = mutableListOf<Prisoner>()
            val wards = listOf("第一监区", "第二监区", "第三监区", "第四监区", "第五监区")
            val firstNames = listOf("张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴")
            val lastNames = listOf("伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀英", "霞", "平")
            
            for (i in 1..count) {
                val ward = wards.random()
                val number = "J${(1000..9999).random()}${String.format("%03d", i)}"
                val name = firstNames.random() + lastNames.random() + lastNames.random()
                val addedTime = Date(System.currentTimeMillis() - (1..30).random() * 24 * 60 * 60 * 1000L) // 随机过去30天内的添加时间
                
                prisoners.add(
                    Prisoner(
                        id = i.toLong(),
                        ward = ward,
                        number = number,
                        name = name,
                        prescriptionPhotoPath = null,
                        fingerprintTemplate = null,
                        addedTime = addedTime
                    )
                )
            }
            
            return prisoners
        }
        
        /**
         * 为指定罪犯生成模拟服药记录
         * @param prisonerId 罪犯ID
         * @param count 记录数量
         * @return 服药记录列表
         */
        fun generateMockMedicationRecords(prisonerId: Long, count: Int): List<MedicationRecord> {
            val records = mutableListOf<MedicationRecord>()
            
            for (i in 1..count) {
                // 生成过去30天内的时间
                val medicationTime = Date(System.currentTimeMillis() - (1..30).random() * 24 * 60 * 60 * 1000L)
                
                records.add(
                    MedicationRecord(
                        id = i.toLong(),
                        prisonerId = prisonerId,
                        medicationTime = medicationTime,
                        fingerprintPath = null,
                        facePhotoPath = null,
                        prescriptionPhotoPath = null
                    )
                )
            }
            
            return records
        }
        
        /**
         * 生成统计数据
         * @return 统计数据
         */
        fun generateMockStatistics(): Statistics {
            val wards = mapOf(
                "第一监区" to (50..150).random(),
                "第二监区" to (50..150).random(),
                "第三监区" to (50..150).random(),
                "第四监区" to (50..150).random(),
                "第五监区" to (50..150).random()
            )
            
            val totalMedications = wards.values.sum()
            
            return Statistics(
                period = "最近30天",
                totalPrisoners = (200..800).random(),
                totalMedications = totalMedications,
                medicationsByWard = wards
            )
        }
    }
}